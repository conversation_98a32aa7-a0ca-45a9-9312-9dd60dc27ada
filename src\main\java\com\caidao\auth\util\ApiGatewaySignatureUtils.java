package com.caidao.auth.util;

import org.springframework.http.HttpHeaders;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Map;
import java.util.TreeMap;

/**
 * API Gateway Signature Utilities
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ApiGatewaySignatureUtils {

    private static final String HMAC_SHA256 = "HmacSHA256";

    /**
     * Generate signature for API Gateway request
     */
    public static String generateSignature(String httpMethod, String path, HttpHeaders headers, 
                                         String body, String secretKey) {
        try {
            // Build string to sign
            StringBuilder stringToSign = new StringBuilder();
            stringToSign.append(httpMethod.toUpperCase()).append("\n");
            stringToSign.append(getCanonicalizedHeaders(headers)).append("\n");
            stringToSign.append(path);
            
            if (body != null && !body.isEmpty()) {
                stringToSign.append("\n").append(body);
            }
            
            // Generate HMAC-SHA256 signature
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            
            byte[] signatureBytes = mac.doFinal(stringToSign.toString().getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signatureBytes);
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }

    /**
     * Get canonicalized headers for signature
     */
    private static String getCanonicalizedHeaders(HttpHeaders headers) {
        Map<String, String> sortedHeaders = new TreeMap<>();
        
        for (Map.Entry<String, java.util.List<String>> entry : headers.entrySet()) {
            String headerName = entry.getKey().toLowerCase();
            if (headerName.startsWith("x-ca-")) {
                String headerValue = String.join(",", entry.getValue());
                sortedHeaders.put(headerName, headerValue);
            }
        }
        
        StringBuilder canonicalizedHeaders = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedHeaders.entrySet()) {
            canonicalizedHeaders.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        
        return canonicalizedHeaders.toString();
    }

    /**
     * Generate nonce for request
     */
    public static String generateNonce() {
        return java.util.UUID.randomUUID().toString();
    }

    /**
     * Get current timestamp
     */
    public static String getCurrentTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }
}
