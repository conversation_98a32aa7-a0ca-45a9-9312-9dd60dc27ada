package com.caidao.auth.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao.auth.entity.UserRole;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * User Role Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
@Repository
public interface UserRoleRepository extends BaseMapper<UserRole>, JpaRepository<UserRole, Long> {

    /**
     * Find user roles by user id
     */
    @Select("SELECT * FROM sys_user_role WHERE user_id = #{userId}")
    List<UserRole> findByUserId(@Param("userId") Long userId);

    /**
     * Find user roles by role id
     */
    @Select("SELECT * FROM sys_user_role WHERE role_id = #{roleId}")
    List<UserRole> findByRoleId(@Param("roleId") Long roleId);

    /**
     * Delete user roles by user id
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId}")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * Delete user roles by role id
     */
    @Delete("DELETE FROM sys_user_role WHERE role_id = #{roleId}")
    void deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * Check if user has role
     */
    @Select("SELECT COUNT(*) > 0 FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    boolean existsByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
}
