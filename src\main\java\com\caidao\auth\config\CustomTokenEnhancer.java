package com.caidao.auth.config;

import com.caidao.auth.entity.User;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom Token Enhancer to add additional information to JWT tokens
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CustomTokenEnhancer implements TokenEnhancer {

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Map<String, Object> additionalInfo = new HashMap<>();
        
        // Add timestamp
        additionalInfo.put("timestamp", System.currentTimeMillis());
        
        // Add issuer
        additionalInfo.put("iss", "caidao-auth-service");
        
        // Add user information if available
        if (authentication.getPrincipal() instanceof User) {
            User user = (User) authentication.getPrincipal();
            additionalInfo.put("user_id", user.getId());
            additionalInfo.put("username", user.getUsername());
            additionalInfo.put("email", user.getEmail());
            additionalInfo.put("roles", user.getAuthorities());
        }
        
        // Add client information
        if (authentication.getOAuth2Request() != null) {
            additionalInfo.put("client_id", authentication.getOAuth2Request().getClientId());
            additionalInfo.put("scope", authentication.getOAuth2Request().getScope());
        }
        
        ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
        return accessToken;
    }
}
