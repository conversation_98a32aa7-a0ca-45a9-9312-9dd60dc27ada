package com.caidao.auth.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao.auth.entity.OAuthClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * OAuth Client Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
@Repository
public interface OAuthClientRepository extends BaseMapper<OAuthClient>, JpaRepository<OAuthClient, String> {

    /**
     * Find client by client id
     */
    @Select("SELECT * FROM oauth_client_details WHERE client_id = #{clientId} AND deleted = 0")
    Optional<OAuthClient> findByClientId(@Param("clientId") String clientId);

    /**
     * Find all enabled clients
     */
    @Select("SELECT * FROM oauth_client_details WHERE enabled = 1 AND deleted = 0")
    List<OAuthClient> findAllEnabled();

    /**
     * Check if client id exists
     */
    @Select("SELECT COUNT(*) > 0 FROM oauth_client_details WHERE client_id = #{clientId} AND deleted = 0")
    boolean existsByClientId(@Param("clientId") String clientId);
}
