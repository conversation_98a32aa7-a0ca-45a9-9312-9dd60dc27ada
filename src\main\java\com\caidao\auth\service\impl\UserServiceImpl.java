package com.caidao.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidao.auth.dto.UserDto;
import com.caidao.auth.entity.Role;
import com.caidao.auth.entity.User;
import com.caidao.auth.entity.UserRole;
import com.caidao.auth.repository.RoleRepository;
import com.caidao.auth.repository.UserRepository;
import com.caidao.auth.repository.UserRoleRepository;
import com.caidao.auth.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * User Service Implementation
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public User createUser(UserDto userDto) {
        // Check if username already exists
        if (userRepository.existsByUsername(userDto.getUsername())) {
            throw new RuntimeException("Username already exists: " + userDto.getUsername());
        }

        // Check if email already exists
        if (userRepository.existsByEmail(userDto.getEmail())) {
            throw new RuntimeException("Email already exists: " + userDto.getEmail());
        }

        User user = userDto.toEntity();
        user.setPassword(passwordEncoder.encode(userDto.getPassword()));
        user.setCreatedTime(LocalDateTime.now());
        user.setUpdatedTime(LocalDateTime.now());
        user.setLoginCount(0);

        userRepository.insert(user);

        // Assign default USER role
        Role userRole = roleRepository.findByName("USER").orElse(null);
        if (userRole != null) {
            assignRole(user.getId(), userRole.getId());
        }

        logger.info("User created successfully: {}", user.getUsername());
        return user;
    }

    @Override
    public User findById(Long id) {
        User user = userRepository.selectById(id);
        if (user != null) {
            // Load roles
            List<Role> roles = roleRepository.findByUserId(id);
            user.setRoles(roles);
        }
        return user;
    }

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username).orElse(null);
    }

    @Override
    public User findByEmail(String email) {
        return userRepository.findByEmail(email).orElse(null);
    }

    @Override
    public User updateUser(Long id, UserDto userDto) {
        User existingUser = userRepository.selectById(id);
        if (existingUser == null) {
            throw new RuntimeException("User not found: " + id);
        }

        // Check username uniqueness if changed
        if (!existingUser.getUsername().equals(userDto.getUsername()) &&
            userRepository.existsByUsername(userDto.getUsername())) {
            throw new RuntimeException("Username already exists: " + userDto.getUsername());
        }

        // Check email uniqueness if changed
        if (!existingUser.getEmail().equals(userDto.getEmail()) &&
            userRepository.existsByEmail(userDto.getEmail())) {
            throw new RuntimeException("Email already exists: " + userDto.getEmail());
        }

        // Update fields
        existingUser.setUsername(userDto.getUsername());
        existingUser.setEmail(userDto.getEmail());
        existingUser.setPhone(userDto.getPhone());
        existingUser.setRealName(userDto.getRealName());
        existingUser.setAvatar(userDto.getAvatar());
        if (userDto.getStatus() != null) {
            existingUser.setStatus(userDto.getStatus());
        }
        existingUser.setUpdatedTime(LocalDateTime.now());

        userRepository.updateById(existingUser);
        logger.info("User updated successfully: {}", existingUser.getUsername());
        return existingUser;
    }

    @Override
    public void deleteUser(Long id) {
        User user = userRepository.selectById(id);
        if (user == null) {
            throw new RuntimeException("User not found: " + id);
        }

        // Remove user roles
        userRoleRepository.deleteByUserId(id);

        // Soft delete user
        userRepository.deleteById(id);
        logger.info("User deleted successfully: {}", id);
    }

    @Override
    public List<User> findAll(int page, int size) {
        Page<User> userPage = new Page<>(page + 1, size);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", false);
        queryWrapper.orderByDesc("created_time");

        Page<User> result = userRepository.selectPage(userPage, queryWrapper);
        return result.getRecords();
    }

    @Override
    public void changePassword(Long id, String oldPassword, String newPassword) {
        User user = userRepository.selectById(id);
        if (user == null) {
            throw new RuntimeException("User not found: " + id);
        }

        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("Old password is incorrect");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.updateById(user);

        logger.info("Password changed successfully for user: {}", id);
    }

    @Override
    public String resetPassword(Long id) {
        User user = userRepository.selectById(id);
        if (user == null) {
            throw new RuntimeException("User not found: " + id);
        }

        String newPassword = UUID.randomUUID().toString().substring(0, 8);
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.updateById(user);

        logger.info("Password reset successfully for user: {}", id);
        return newPassword;
    }

    @Override
    public void updateUserStatus(Long id, User.UserStatus status) {
        User user = userRepository.selectById(id);
        if (user == null) {
            throw new RuntimeException("User not found: " + id);
        }

        user.setStatus(status);
        user.setUpdatedTime(LocalDateTime.now());
        userRepository.updateById(user);

        logger.info("User status updated successfully: {} -> {}", id, status);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public void assignRole(Long userId, Long roleId) {
        if (!userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            UserRole userRole = new UserRole(userId, roleId);
            userRole.setCreatedTime(LocalDateTime.now());
            userRoleRepository.insert(userRole);
            logger.debug("Role assigned to user: {} -> {}", userId, roleId);
        }
    }

    @Override
    public void removeRole(Long userId, Long roleId) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("role_id", roleId);
        userRoleRepository.delete(queryWrapper);
        logger.debug("Role removed from user: {} -> {}", userId, roleId);
    }
}
