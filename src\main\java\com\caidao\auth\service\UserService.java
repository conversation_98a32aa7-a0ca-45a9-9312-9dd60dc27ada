package com.caidao.auth.service;

import com.caidao.auth.dto.UserDto;
import com.caidao.auth.entity.User;

import java.util.List;

/**
 * User Service Interface
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface UserService {

    /**
     * Create new user
     */
    User createUser(UserDto userDto);

    /**
     * Find user by ID
     */
    User findById(Long id);

    /**
     * Find user by username
     */
    User findByUsername(String username);

    /**
     * Find user by email
     */
    User findByEmail(String email);

    /**
     * Update user
     */
    User updateUser(Long id, UserDto userDto);

    /**
     * Delete user
     */
    void deleteUser(Long id);

    /**
     * Find all users with pagination
     */
    List<User> findAll(int page, int size);

    /**
     * Change user password
     */
    void changePassword(Long id, String oldPassword, String newPassword);

    /**
     * Reset user password
     */
    String resetPassword(Long id);

    /**
     * Update user status
     */
    void updateUserStatus(Long id, User.UserStatus status);

    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Assign role to user
     */
    void assignRole(Long userId, Long roleId);

    /**
     * Remove role from user
     */
    void removeRole(Long userId, Long roleId);
}
