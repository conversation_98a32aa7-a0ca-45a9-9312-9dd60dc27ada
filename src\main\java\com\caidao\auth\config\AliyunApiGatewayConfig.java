package com.caidao.auth.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Alibaba Cloud API Gateway Configuration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration

public class AliyunApiGatewayConfig {

    @Value("${aliyun.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.api-gateway.endpoint}")
    private String apiGatewayEndpoint;

    @Value("${aliyun.api-gateway.group-id}")
    private String groupId;

    /**
     * Rest template for API Gateway calls
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getApiGatewayEndpoint() {
        return apiGatewayEndpoint;
    }

    public String getGroupId() {
        return groupId;
    }
}
