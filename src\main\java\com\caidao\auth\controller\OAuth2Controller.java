package com.caidao.auth.controller;

import com.caidao.auth.dto.ApiResponse;
import com.caidao.auth.service.CustomClientDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.endpoint.TokenEndpoint;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2 Controller
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/oauth2")
public class OAuth2Controller {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2Controller.class);

    @Autowired
    private TokenEndpoint tokenEndpoint;

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private CustomClientDetailsService clientDetailsService;

    /**
     * Get access token
     */
    @PostMapping("/token")
    public ApiResponse<OAuth2AccessToken> getAccessToken(Principal principal, 
                                                        @RequestParam Map<String, String> parameters,
                                                        HttpServletRequest request) {
        try {
            OAuth2AccessToken token = tokenEndpoint.postAccessToken(principal, parameters).getBody();
            logger.info("Access token generated successfully for client: {}", parameters.get("client_id"));
            return ApiResponse.success(token);
        } catch (HttpRequestMethodNotSupportedException e) {
            logger.error("Failed to generate access token", e);
            return ApiResponse.error("Token generation failed: " + e.getMessage());
        }
    }

    /**
     * Revoke access token
     */
    @PostMapping("/revoke")
    public ApiResponse<String> revokeToken(@RequestParam("token") String token,
                                          @RequestParam(value = "token_type_hint", required = false) String tokenTypeHint) {
        try {
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            if (accessToken != null) {
                tokenStore.removeAccessToken(accessToken);
                if (accessToken.getRefreshToken() != null) {
                    tokenStore.removeRefreshToken(accessToken.getRefreshToken());
                }
                logger.info("Token revoked successfully");
                return ApiResponse.success("Token revoked successfully");
            } else {
                return ApiResponse.error("Token not found");
            }
        } catch (Exception e) {
            logger.error("Failed to revoke token", e);
            return ApiResponse.error("Token revocation failed: " + e.getMessage());
        }
    }

    /**
     * Introspect token
     */
    @PostMapping("/introspect")
    public ApiResponse<Map<String, Object>> introspectToken(@RequestParam("token") String token) {
        try {
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            Map<String, Object> result = new HashMap<>();
            
            if (accessToken != null && !accessToken.isExpired()) {
                result.put("active", true);
                result.put("client_id", accessToken.getAdditionalInformation().get("client_id"));
                result.put("username", accessToken.getAdditionalInformation().get("username"));
                result.put("scope", accessToken.getScope());
                result.put("exp", accessToken.getExpiration().getTime() / 1000);
                result.put("iat", System.currentTimeMillis() / 1000);
                result.put("token_type", accessToken.getTokenType());
            } else {
                result.put("active", false);
            }
            
            logger.debug("Token introspection completed");
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("Failed to introspect token", e);
            return ApiResponse.error("Token introspection failed: " + e.getMessage());
        }
    }

    /**
     * Get user info
     */
    @GetMapping("/userinfo")
    public ApiResponse<Map<String, Object>> getUserInfo(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error("Invalid authorization header");
            }
            
            String token = authHeader.substring(7);
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            
            if (accessToken == null || accessToken.isExpired()) {
                return ApiResponse.error("Invalid or expired token");
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("sub", accessToken.getAdditionalInformation().get("user_id"));
            userInfo.put("username", accessToken.getAdditionalInformation().get("username"));
            userInfo.put("email", accessToken.getAdditionalInformation().get("email"));
            userInfo.put("roles", accessToken.getAdditionalInformation().get("roles"));
            
            logger.debug("User info retrieved successfully");
            return ApiResponse.success(userInfo);
        } catch (Exception e) {
            logger.error("Failed to get user info", e);
            return ApiResponse.error("Failed to get user info: " + e.getMessage());
        }
    }

    /**
     * Get client info
     */
    @GetMapping("/clients/{clientId}")
    public ApiResponse<Map<String, Object>> getClientInfo(@PathVariable String clientId) {
        try {
            if (!clientDetailsService.existsByClientId(clientId)) {
                return ApiResponse.error("Client not found");
            }
            
            Map<String, Object> clientInfo = new HashMap<>();
            clientInfo.put("client_id", clientId);
            clientInfo.put("status", "active");
            
            logger.debug("Client info retrieved successfully: {}", clientId);
            return ApiResponse.success(clientInfo);
        } catch (Exception e) {
            logger.error("Failed to get client info: {}", clientId, e);
            return ApiResponse.error("Failed to get client info: " + e.getMessage());
        }
    }

    /**
     * Health check
     */
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "caidao-auth-service");
        health.put("version", "1.0.0");
        
        return ApiResponse.success(health);
    }
}
