package com.caidao.auth.service;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.kms.model.v20160120.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Alibaba Cloud KMS Service
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class AliyunKmsService {

    private static final Logger logger = LoggerFactory.getLogger(AliyunKmsService.class);

    @Autowired
    private IAcsClient kmsClient;

    @Value("${aliyun.kms.key-id}")
    private String keyId;

    /**
     * Encrypt data using KMS
     */
    public String encrypt(String plaintext) {
        try {
            EncryptRequest request = new EncryptRequest();
            request.setKeyId(keyId);
            request.setPlaintext(Base64.getEncoder().encodeToString(plaintext.getBytes(StandardCharsets.UTF_8)));

            EncryptResponse response = kmsClient.getAcsResponse(request);
            logger.debug("Data encrypted successfully");
            return response.getCiphertextBlob();
        } catch (ClientException e) {
            logger.error("Failed to encrypt data", e);
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * Decrypt data using KMS
     */
    public String decrypt(String ciphertext) {
        try {
            DecryptRequest request = new DecryptRequest();
            request.setCiphertextBlob(ciphertext);

            DecryptResponse response = kmsClient.getAcsResponse(request);
            String decryptedData = new String(Base64.getDecoder().decode(response.getPlaintext()), StandardCharsets.UTF_8);
            logger.debug("Data decrypted successfully");
            return decryptedData;
        } catch (ClientException e) {
            logger.error("Failed to decrypt data", e);
            throw new RuntimeException("Decryption failed", e);
        }
    }

    /**
     * Generate data key for envelope encryption
     */
    public GenerateDataKeyResponse generateDataKey() {
        try {
            GenerateDataKeyRequest request = new GenerateDataKeyRequest();
            request.setKeyId(keyId);
            request.setKeySpec("AES_256");

            GenerateDataKeyResponse response = kmsClient.getAcsResponse(request);
            logger.debug("Data key generated successfully");
            return response;
        } catch (ClientException e) {
            logger.error("Failed to generate data key", e);
            throw new RuntimeException("Data key generation failed", e);
        }
    }

    /**
     * Create alias for key
     */
    public void createAlias(String aliasName) {
        try {
            CreateAliasRequest request = new CreateAliasRequest();
            request.setKeyId(keyId);
            request.setAliasName(aliasName);

            kmsClient.getAcsResponse(request);
            logger.info("Alias created successfully: {}", aliasName);
        } catch (ClientException e) {
            logger.error("Failed to create alias: {}", aliasName, e);
            throw new RuntimeException("Alias creation failed", e);
        }
    }

    /**
     * Get key description
     */
    public DescribeKeyResponse describeKey() {
        try {
            DescribeKeyRequest request = new DescribeKeyRequest();
            request.setKeyId(keyId);

            DescribeKeyResponse response = kmsClient.getAcsResponse(request);
            logger.debug("Key description retrieved successfully");
            return response;
        } catch (ClientException e) {
            logger.error("Failed to describe key", e);
            throw new RuntimeException("Key description failed", e);
        }
    }

    /**
     * Enable key
     */
    public void enableKey() {
        try {
            EnableKeyRequest request = new EnableKeyRequest();
            request.setKeyId(keyId);

            kmsClient.getAcsResponse(request);
            logger.info("Key enabled successfully");
        } catch (ClientException e) {
            logger.error("Failed to enable key", e);
            throw new RuntimeException("Key enable failed", e);
        }
    }

    /**
     * Disable key
     */
    public void disableKey() {
        try {
            DisableKeyRequest request = new DisableKeyRequest();
            request.setKeyId(keyId);

            kmsClient.getAcsResponse(request);
            logger.info("Key disabled successfully");
        } catch (ClientException e) {
            logger.error("Failed to disable key", e);
            throw new RuntimeException("Key disable failed", e);
        }
    }
}
