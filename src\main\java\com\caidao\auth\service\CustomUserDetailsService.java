package com.caidao.auth.service;

import com.caidao.auth.entity.Role;
import com.caidao.auth.entity.User;
import com.caidao.auth.repository.RoleRepository;
import com.caidao.auth.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Custom User Details Service
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@Transactional
public class CustomUserDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomUserDetailsService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Loading user by username: {}", username);

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));

        // Load user roles
        List<Role> roles = roleRepository.findByUserId(user.getId());
        user.setRoles(roles);

        logger.debug("User loaded successfully: {} with {} roles", username, roles.size());
        return user;
    }

    /**
     * Load user by email
     */
    public UserDetails loadUserByEmail(String email) throws UsernameNotFoundException {
        logger.debug("Loading user by email: {}", email);

        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + email));

        // Load user roles
        List<Role> roles = roleRepository.findByUserId(user.getId());
        user.setRoles(roles);

        logger.debug("User loaded successfully by email: {} with {} roles", email, roles.size());
        return user;
    }

    /**
     * Update user last login information
     */
    public void updateLastLogin(String username, String ipAddress) {
        try {
            User user = userRepository.findByUsername(username).orElse(null);
            if (user != null) {
                user.setLastLoginTime(LocalDateTime.now());
                user.setLastLoginIp(ipAddress);
                user.setLoginCount(user.getLoginCount() + 1);
                userRepository.updateById(user);
                logger.debug("Updated last login for user: {}", username);
            }
        } catch (Exception e) {
            logger.error("Failed to update last login for user: {}", username, e);
        }
    }

    /**
     * Check if user exists by username
     */
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    /**
     * Check if user exists by email
     */
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    /**
     * Find user by password reset token
     */
    public User findByPasswordResetToken(String token) {
        return userRepository.findByPasswordResetToken(token).orElse(null);
    }
}
