package com.caidao.auth.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Alibaba Cloud KMS Configuration
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
public class AliyunKmsConfig {

    @Value("${aliyun.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.region-id}")
    private String regionId;

    @Value("${aliyun.kms.endpoint}")
    private String kmsEndpoint;

    /**
     * Create KMS client
     */
    @Bean
    public IAcsClient kmsClient() {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        profile.addEndpoint(regionId, "Kms", kmsEndpoint);
        return new DefaultAcsClient(profile);
    }
}
