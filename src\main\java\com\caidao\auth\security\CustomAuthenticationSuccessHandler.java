package com.caidao.auth.security;

import com.alibaba.fastjson.JSON;
import com.caidao.auth.service.CustomUserDetailsService;
import com.caidao.auth.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Custom Authentication Success Handler
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class CustomAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationSuccessHandler.class);

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                      Authentication authentication) throws IOException, ServletException {
        
        String username = authentication.getName();
        String ipAddress = WebUtils.getClientIpAddress(request);
        
        logger.info("User {} logged in successfully from IP: {}", username, ipAddress);
        
        // Update last login information
        userDetailsService.updateLastLogin(username, ipAddress);
        
        // Check if it's an AJAX request
        if (WebUtils.isAjaxRequest(request)) {
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_OK);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Login successful");
            result.put("username", username);
            result.put("timestamp", System.currentTimeMillis());
            
            response.getWriter().write(JSON.toJSONString(result));
        } else {
            // Redirect to the original requested URL or default success URL
            String targetUrl = determineTargetUrl(request, response, authentication);
            response.sendRedirect(targetUrl);
        }
    }

    /**
     * Determine the target URL after successful authentication
     */
    private String determineTargetUrl(HttpServletRequest request, HttpServletResponse response,
                                    Authentication authentication) {
        
        // Check for saved request
        String targetUrl = (String) request.getSession().getAttribute("SPRING_SECURITY_SAVED_REQUEST");
        if (targetUrl != null) {
            request.getSession().removeAttribute("SPRING_SECURITY_SAVED_REQUEST");
            return targetUrl;
        }
        
        // Default success URL
        return "/oauth/authorize";
    }
}
