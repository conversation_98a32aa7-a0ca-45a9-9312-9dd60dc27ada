package com.caidao.auth.security;

import com.alibaba.fastjson.JSON;
import com.caidao.auth.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Custom Authentication Failure Handler
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationFailureHandler.class);

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                      AuthenticationException exception) throws IOException, ServletException {
        
        String username = request.getParameter("username");
        String ipAddress = WebUtils.getClientIpAddress(request);
        
        logger.warn("Authentication failed for user {} from IP: {}, reason: {}", 
                   username, ipAddress, exception.getMessage());
        
        String errorMessage = getErrorMessage(exception);
        
        // Check if it's an AJAX request
        if (WebUtils.isAjaxRequest(request)) {
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", errorMessage);
            result.put("error", exception.getClass().getSimpleName());
            result.put("timestamp", System.currentTimeMillis());
            
            response.getWriter().write(JSON.toJSONString(result));
        } else {
            // Redirect to login page with error
            String redirectUrl = "/login?error=" + java.net.URLEncoder.encode(errorMessage, "UTF-8");
            response.sendRedirect(redirectUrl);
        }
    }

    /**
     * Get user-friendly error message based on exception type
     */
    private String getErrorMessage(AuthenticationException exception) {
        if (exception instanceof UsernameNotFoundException) {
            return "用户名不存在";
        } else if (exception instanceof BadCredentialsException) {
            return "用户名或密码错误";
        } else if (exception instanceof AccountExpiredException) {
            return "账户已过期";
        } else if (exception instanceof LockedException) {
            return "账户已被锁定";
        } else if (exception instanceof DisabledException) {
            return "账户已被禁用";
        } else if (exception instanceof CredentialsExpiredException) {
            return "密码已过期";
        } else {
            return "登录失败，请稍后重试";
        }
    }
}
