package com.caidao.auth.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao.auth.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Role Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
@Repository
public interface RoleRepository extends BaseMapper<Role>, JpaRepository<Role, Long> {

    /**
     * Find role by name
     */
    @Select("SELECT * FROM sys_role WHERE name = #{name} AND deleted = 0")
    Optional<Role> findByName(@Param("name") String name);

    /**
     * Find all enabled roles
     */
    @Select("SELECT * FROM sys_role WHERE enabled = 1 AND deleted = 0")
    List<Role> findAllEnabled();

    /**
     * Find roles by user id
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.enabled = 1 AND r.deleted = 0")
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * Check if role name exists
     */
    @Select("SELECT COUNT(*) > 0 FROM sys_role WHERE name = #{name} AND deleted = 0")
    boolean existsByName(@Param("name") String name);
}
