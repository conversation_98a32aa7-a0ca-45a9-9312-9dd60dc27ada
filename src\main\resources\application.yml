server:
  port: 8080
  servlet:
    context-path: /auth

spring:
  application:
    name: caidao-open-auth-service
  
  # Database Configuration
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
        allow: 127.0.0.1

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Redis Configuration
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# MyBatis Plus Configuration
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.caidao.auth.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Alibaba Cloud Configuration
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
  region-id: ${ALIYUN_REGION_ID:cn-hangzhou}
  
  # KMS Configuration
  kms:
    endpoint: ${ALIYUN_KMS_ENDPOINT:kms.cn-hangzhou.aliyuncs.com}
    key-id: ${ALIYUN_KMS_KEY_ID:your-kms-key-id}
  
  # API Gateway Configuration
  api-gateway:
    endpoint: ${ALIYUN_API_GATEWAY_ENDPOINT:apigateway.cn-hangzhou.aliyuncs.com}
    group-id: ${ALIYUN_API_GATEWAY_GROUP_ID:your-group-id}

# OAuth2 Configuration
oauth2:
  jwt:
    signing-key: ${JWT_SIGNING_KEY:caidao-oauth2-jwt-signing-key-2023}
    access-token-validity-seconds: 7200  # 2 hours
    refresh-token-validity-seconds: 2592000  # 30 days
  
  # Default OAuth2 Clients
  clients:
    - client-id: caidao-web-client
      client-secret: caidao-web-secret
      authorized-grant-types: authorization_code,refresh_token,password,client_credentials
      scope: read,write
      redirect-uris: http://localhost:3000/callback
      access-token-validity: 7200
      refresh-token-validity: 2592000
    
    - client-id: caidao-mobile-client
      client-secret: caidao-mobile-secret
      authorized-grant-types: authorization_code,refresh_token,password
      scope: read,write
      redirect-uris: caidao://oauth/callback
      access-token-validity: 7200
      refresh-token-validity: 2592000

# Security Configuration
security:
  # Password encryption
  password:
    encoder: bcrypt
    strength: 12
  
  # Session configuration
  session:
    timeout: 1800  # 30 minutes
    max-sessions: 1
    prevent-login-if-maximum-exceeded: false

# Logging Configuration
logging:
  level:
    com.caidao.auth: DEBUG
    org.springframework.security: DEBUG
    org.springframework.security.oauth2: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/caidao-auth.log
    max-size: 100MB
    max-history: 30

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Application Information
info:
  app:
    name: Caidao Open Auth Service
    description: OAuth 2.0 Authorization Service with Alibaba Cloud Integration
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 1.8
