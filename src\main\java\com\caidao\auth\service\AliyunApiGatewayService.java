package com.caidao.auth.service;

import com.caidao.auth.config.AliyunApiGatewayConfig;
import com.caidao.auth.util.ApiGatewaySignatureUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Alibaba Cloud API Gateway Service
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class AliyunApiGatewayService {

    private static final Logger logger = LoggerFactory.getLogger(AliyunApiGatewayService.class);

    @Autowired
    private AliyunApiGatewayConfig apiGatewayConfig;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * Call API Gateway with signature authentication
     */
    public ResponseEntity<String> callApi(String path, HttpMethod method, Object requestBody) {
        try {
            String url = apiGatewayConfig.getApiGatewayEndpoint() + path;
            
            // Create headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // Add timestamp
            String timestamp = String.valueOf(System.currentTimeMillis());
            headers.add("X-Ca-Timestamp", timestamp);
            
            // Add nonce
            String nonce = java.util.UUID.randomUUID().toString();
            headers.add("X-Ca-Nonce", nonce);
            
            // Add signature version
            headers.add("X-Ca-Signature-Method", "HmacSHA256");
            
            // Generate signature
            String signature = ApiGatewaySignatureUtils.generateSignature(
                method.name(),
                path,
                headers,
                requestBody != null ? requestBody.toString() : "",
                apiGatewayConfig.getAccessKeySecret()
            );
            
            headers.add("Authorization", "acs " + apiGatewayConfig.getAccessKeyId() + ":" + signature);
            
            // Create request entity
            HttpEntity<Object> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // Make the call
            ResponseEntity<String> response = restTemplate.exchange(url, method, requestEntity, String.class);
            
            logger.debug("API Gateway call successful: {} {}", method, path);
            return response;
            
        } catch (Exception e) {
            logger.error("API Gateway call failed: {} {}", method, path, e);
            throw new RuntimeException("API Gateway call failed", e);
        }
    }

    /**
     * Register OAuth2 endpoints with API Gateway
     */
    public void registerOAuth2Endpoints() {
        try {
            Map<String, Object> apiDefinition = new HashMap<>();
            apiDefinition.put("groupId", apiGatewayConfig.getGroupId());
            apiDefinition.put("apiName", "OAuth2 Authorization");
            apiDefinition.put("apiPath", "/oauth/*");
            apiDefinition.put("httpMethod", "ANY");
            apiDefinition.put("backendType", "HTTP");
            apiDefinition.put("backendUrl", "http://localhost:8080/auth/oauth");
            
            ResponseEntity<String> response = callApi("/apis", HttpMethod.POST, apiDefinition);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("OAuth2 endpoints registered successfully with API Gateway");
            } else {
                logger.warn("Failed to register OAuth2 endpoints: {}", response.getBody());
            }
            
        } catch (Exception e) {
            logger.error("Failed to register OAuth2 endpoints with API Gateway", e);
        }
    }

    /**
     * Update API Gateway configuration
     */
    public void updateApiConfiguration(String apiId, Map<String, Object> config) {
        try {
            String path = "/apis/" + apiId;
            ResponseEntity<String> response = callApi(path, HttpMethod.PUT, config);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("API configuration updated successfully: {}", apiId);
            } else {
                logger.warn("Failed to update API configuration: {}", response.getBody());
            }
            
        } catch (Exception e) {
            logger.error("Failed to update API configuration: {}", apiId, e);
        }
    }

    /**
     * Get API Gateway statistics
     */
    public Map<String, Object> getApiStatistics(String apiId) {
        try {
            String path = "/apis/" + apiId + "/statistics";
            ResponseEntity<String> response = callApi(path, HttpMethod.GET, null);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.debug("API statistics retrieved successfully: {}", apiId);
                // Parse response and return statistics
                return new HashMap<>(); // Simplified for demo
            } else {
                logger.warn("Failed to get API statistics: {}", response.getBody());
                return new HashMap<>();
            }
            
        } catch (Exception e) {
            logger.error("Failed to get API statistics: {}", apiId, e);
            return new HashMap<>();
        }
    }
}
