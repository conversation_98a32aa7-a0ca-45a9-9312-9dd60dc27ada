package com.caidao.auth.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidao.auth.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * User Repository
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
@Repository
public interface UserRepository extends BaseMapper<User>, JpaRepository<User, Long> {

    /**
     * Find user by username
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND deleted = 0")
    Optional<User> findByUsername(@Param("username") String username);

    /**
     * Find user by email
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND deleted = 0")
    Optional<User> findByEmail(@Param("email") String email);

    /**
     * Find user by phone
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND deleted = 0")
    Optional<User> findByPhone(@Param("phone") String phone);

    /**
     * Find user by password reset token
     */
    @Select("SELECT * FROM sys_user WHERE password_reset_token = #{token} AND deleted = 0")
    Optional<User> findByPasswordResetToken(@Param("token") String token);

    /**
     * Check if username exists
     */
    @Select("SELECT COUNT(*) > 0 FROM sys_user WHERE username = #{username} AND deleted = 0")
    boolean existsByUsername(@Param("username") String username);

    /**
     * Check if email exists
     */
    @Select("SELECT COUNT(*) > 0 FROM sys_user WHERE email = #{email} AND deleted = 0")
    boolean existsByEmail(@Param("email") String email);

    /**
     * Check if phone exists
     */
    @Select("SELECT COUNT(*) > 0 FROM sys_user WHERE phone = #{phone} AND deleted = 0")
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * Find user with roles by username
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.roles WHERE u.username = :username AND u.deleted = false")
    Optional<User> findByUsernameWithRoles(@Param("username") String username);

    /**
     * Find user with roles by email
     */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.roles WHERE u.email = :email AND u.deleted = false")
    Optional<User> findByEmailWithRoles(@Param("email") String email);
}
