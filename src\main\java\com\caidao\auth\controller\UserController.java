package com.caidao.auth.controller;

import com.caidao.auth.dto.ApiResponse;
import com.caidao.auth.dto.UserDto;
import com.caidao.auth.entity.User;
import com.caidao.auth.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * User Management Controller
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * Create new user
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<UserDto> createUser(@Valid @RequestBody UserDto userDto) {
        try {
            User user = userService.createUser(userDto);
            logger.info("User created successfully: {}", user.getUsername());
            return ApiResponse.success(UserDto.fromEntity(user));
        } catch (Exception e) {
            logger.error("Failed to create user", e);
            return ApiResponse.error("Failed to create user: " + e.getMessage());
        }
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ApiResponse<UserDto> getUserById(@PathVariable Long id) {
        try {
            User user = userService.findById(id);
            if (user == null) {
                return ApiResponse.error("User not found");
            }
            return ApiResponse.success(UserDto.fromEntity(user));
        } catch (Exception e) {
            logger.error("Failed to get user by ID: {}", id, e);
            return ApiResponse.error("Failed to get user: " + e.getMessage());
        }
    }

    /**
     * Get user by username
     */
    @GetMapping("/username/{username}")
    @PreAuthorize("hasRole('ADMIN') or #username == authentication.principal.username")
    public ApiResponse<UserDto> getUserByUsername(@PathVariable String username) {
        try {
            User user = userService.findByUsername(username);
            if (user == null) {
                return ApiResponse.error("User not found");
            }
            return ApiResponse.success(UserDto.fromEntity(user));
        } catch (Exception e) {
            logger.error("Failed to get user by username: {}", username, e);
            return ApiResponse.error("Failed to get user: " + e.getMessage());
        }
    }

    /**
     * Update user
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ApiResponse<UserDto> updateUser(@PathVariable Long id, @Valid @RequestBody UserDto userDto) {
        try {
            User user = userService.updateUser(id, userDto);
            logger.info("User updated successfully: {}", user.getUsername());
            return ApiResponse.success(UserDto.fromEntity(user));
        } catch (Exception e) {
            logger.error("Failed to update user: {}", id, e);
            return ApiResponse.error("Failed to update user: " + e.getMessage());
        }
    }

    /**
     * Delete user
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            logger.info("User deleted successfully: {}", id);
            return ApiResponse.success("User deleted successfully");
        } catch (Exception e) {
            logger.error("Failed to delete user: {}", id, e);
            return ApiResponse.error("Failed to delete user: " + e.getMessage());
        }
    }

    /**
     * Get all users
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<List<UserDto>> getAllUsers(@RequestParam(defaultValue = "0") int page,
                                                 @RequestParam(defaultValue = "20") int size) {
        try {
            List<User> users = userService.findAll(page, size);
            List<UserDto> userDtos = users.stream()
                    .map(UserDto::fromEntity)
                    .collect(java.util.stream.Collectors.toList());
            return ApiResponse.success(userDtos);
        } catch (Exception e) {
            logger.error("Failed to get all users", e);
            return ApiResponse.error("Failed to get users: " + e.getMessage());
        }
    }

    /**
     * Change password
     */
    @PostMapping("/{id}/change-password")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ApiResponse<String> changePassword(@PathVariable Long id,
                                            @RequestParam String oldPassword,
                                            @RequestParam String newPassword) {
        try {
            userService.changePassword(id, oldPassword, newPassword);
            logger.info("Password changed successfully for user: {}", id);
            return ApiResponse.success("Password changed successfully");
        } catch (Exception e) {
            logger.error("Failed to change password for user: {}", id, e);
            return ApiResponse.error("Failed to change password: " + e.getMessage());
        }
    }

    /**
     * Reset password
     */
    @PostMapping("/{id}/reset-password")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> resetPassword(@PathVariable Long id) {
        try {
            String newPassword = userService.resetPassword(id);
            logger.info("Password reset successfully for user: {}", id);
            return ApiResponse.success("Password reset successfully. New password: " + newPassword);
        } catch (Exception e) {
            logger.error("Failed to reset password for user: {}", id, e);
            return ApiResponse.error("Failed to reset password: " + e.getMessage());
        }
    }

    /**
     * Enable/Disable user
     */
    @PostMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> updateUserStatus(@PathVariable Long id,
                                              @RequestParam User.UserStatus status) {
        try {
            userService.updateUserStatus(id, status);
            logger.info("User status updated successfully: {} -> {}", id, status);
            return ApiResponse.success("User status updated successfully");
        } catch (Exception e) {
            logger.error("Failed to update user status: {}", id, e);
            return ApiResponse.error("Failed to update user status: " + e.getMessage());
        }
    }
}
