package com.caidao.auth.entity;

import com.baomidou.mybatisplus.annotation.*;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * OAuth2 Client Entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "oauth_client_details")
@TableName("oauth_client_details")
public class OAuthClient {

    @Id
    @Column(name = "client_id", length = 256)
    private String clientId;

    @Column(name = "resource_ids", length = 256)
    private String resourceIds;

    @Column(name = "client_secret", length = 256)
    private String clientSecret;

    @Column(name = "scope", length = 256)
    private String scope;

    @Column(name = "authorized_grant_types", length = 256)
    private String authorizedGrantTypes;

    @Column(name = "web_server_redirect_uri", length = 256)
    private String webServerRedirectUri;

    @Column(name = "authorities", length = 256)
    private String authorities;

    @Column(name = "access_token_validity")
    private Integer accessTokenValidity;

    @Column(name = "refresh_token_validity")
    private Integer refreshTokenValidity;

    @Column(name = "additional_information", length = 4096)
    private String additionalInformation;

    @Column(name = "autoapprove", length = 256)
    private String autoapprove;

    @Column(name = "client_name", length = 100)
    private String clientName;

    @Column(name = "client_description", length = 500)
    private String clientDescription;

    @Column(name = "client_uri", length = 256)
    private String clientUri;

    @Column(name = "logo_uri", length = 256)
    private String logoUri;

    @Column(name = "contact_email", length = 100)
    private String contactEmail;

    @Column(name = "tos_uri", length = 256)
    private String tosUri;

    @Column(name = "policy_uri", length = 256)
    private String policyUri;

    @Column(name = "jwks_uri", length = 256)
    private String jwksUri;

    @Column(name = "sector_identifier_uri", length = 256)
    private String sectorIdentifierUri;

    @Column(name = "subject_type", length = 50)
    private String subjectType;

    @Column(name = "id_token_signed_response_alg", length = 50)
    private String idTokenSignedResponseAlg;

    @Column(name = "id_token_encrypted_response_alg", length = 50)
    private String idTokenEncryptedResponseAlg;

    @Column(name = "id_token_encrypted_response_enc", length = 50)
    private String idTokenEncryptedResponseEnc;

    @Column(name = "userinfo_signed_response_alg", length = 50)
    private String userinfoSignedResponseAlg;

    @Column(name = "userinfo_encrypted_response_alg", length = 50)
    private String userinfoEncryptedResponseAlg;

    @Column(name = "userinfo_encrypted_response_enc", length = 50)
    private String userinfoEncryptedResponseEnc;

    @Column(name = "request_object_signing_alg", length = 50)
    private String requestObjectSigningAlg;

    @Column(name = "request_object_encryption_alg", length = 50)
    private String requestObjectEncryptionAlg;

    @Column(name = "request_object_encryption_enc", length = 50)
    private String requestObjectEncryptionEnc;

    @Column(name = "token_endpoint_auth_method", length = 50)
    private String tokenEndpointAuthMethod;

    @Column(name = "token_endpoint_auth_signing_alg", length = 50)
    private String tokenEndpointAuthSigningAlg;

    @Column(name = "default_max_age")
    private Integer defaultMaxAge;

    @Column(name = "require_auth_time")
    private Boolean requireAuthTime = false;

    @Column(name = "default_acr_values", length = 256)
    private String defaultAcrValues;

    @Column(name = "initiate_login_uri", length = 256)
    private String initiateLoginUri;

    @Column(name = "request_uris", length = 1000)
    private String requestUris;

    @Column(name = "enabled")
    private Boolean enabled = true;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    @TableLogic
    @Column(nullable = false)
    private Boolean deleted = false;

    // Constructors
    public OAuthClient() {
    }

    public OAuthClient(String clientId, String clientSecret, String scope, String authorizedGrantTypes) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.scope = scope;
        this.authorizedGrantTypes = authorizedGrantTypes;
    }

    // Getters and Setters
    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getResourceIds() {
        return resourceIds;
    }

    public void setResourceIds(String resourceIds) {
        this.resourceIds = resourceIds;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getAuthorizedGrantTypes() {
        return authorizedGrantTypes;
    }

    public void setAuthorizedGrantTypes(String authorizedGrantTypes) {
        this.authorizedGrantTypes = authorizedGrantTypes;
    }

    public String getWebServerRedirectUri() {
        return webServerRedirectUri;
    }

    public void setWebServerRedirectUri(String webServerRedirectUri) {
        this.webServerRedirectUri = webServerRedirectUri;
    }

    public String getAuthorities() {
        return authorities;
    }

    public void setAuthorities(String authorities) {
        this.authorities = authorities;
    }

    public Integer getAccessTokenValidity() {
        return accessTokenValidity;
    }

    public void setAccessTokenValidity(Integer accessTokenValidity) {
        this.accessTokenValidity = accessTokenValidity;
    }

    public Integer getRefreshTokenValidity() {
        return refreshTokenValidity;
    }

    public void setRefreshTokenValidity(Integer refreshTokenValidity) {
        this.refreshTokenValidity = refreshTokenValidity;
    }

    public String getAdditionalInformation() {
        return additionalInformation;
    }

    public void setAdditionalInformation(String additionalInformation) {
        this.additionalInformation = additionalInformation;
    }

    public String getAutoapprove() {
        return autoapprove;
    }

    public void setAutoapprove(String autoapprove) {
        this.autoapprove = autoapprove;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientDescription() {
        return clientDescription;
    }

    public void setClientDescription(String clientDescription) {
        this.clientDescription = clientDescription;
    }

    public String getClientUri() {
        return clientUri;
    }

    public void setClientUri(String clientUri) {
        this.clientUri = clientUri;
    }

    public String getLogoUri() {
        return logoUri;
    }

    public void setLogoUri(String logoUri) {
        this.logoUri = logoUri;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getTosUri() {
        return tosUri;
    }

    public void setTosUri(String tosUri) {
        this.tosUri = tosUri;
    }

    public String getPolicyUri() {
        return policyUri;
    }

    public void setPolicyUri(String policyUri) {
        this.policyUri = policyUri;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
