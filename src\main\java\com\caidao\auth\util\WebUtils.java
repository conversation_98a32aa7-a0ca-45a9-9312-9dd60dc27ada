package com.caidao.auth.util;

import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * Web Utilities
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WebUtils {

    private static final String[] IP_HEADER_CANDIDATES = {
        "X-Forwarded-For",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED",
        "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP",
        "HTTP_FORWARDED_FOR",
        "HTTP_FORWARDED",
        "HTTP_VIA",
        "REMOTE_ADDR"
    };

    /**
     * Get client IP address from request
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        for (String header : IP_HEADER_CANDIDATES) {
            String ipList = request.getHeader(header);
            if (StringUtils.hasText(ipList) && !"unknown".equalsIgnoreCase(ipList)) {
                String ip = ipList.split(",")[0];
                return ip.trim();
            }
        }
        return request.getRemoteAddr();
    }

    /**
     * Check if the request is an AJAX request
     */
    public static boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }

    /**
     * Check if the request accepts JSON
     */
    public static boolean acceptsJson(HttpServletRequest request) {
        String accept = request.getHeader("Accept");
        return StringUtils.hasText(accept) && accept.contains("application/json");
    }

    /**
     * Get user agent from request
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * Get referer from request
     */
    public static String getReferer(HttpServletRequest request) {
        return request.getHeader("Referer");
    }
}
