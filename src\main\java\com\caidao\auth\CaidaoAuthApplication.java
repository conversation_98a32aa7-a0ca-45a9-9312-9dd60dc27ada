package com.caidao.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;

/**
 * Caidao OAuth2 Authorization Service Application
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAuthorizationServer
@EnableResourceServer
public class CaidaoAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(CaidaoAuthApplication.class, args);
    }
}
