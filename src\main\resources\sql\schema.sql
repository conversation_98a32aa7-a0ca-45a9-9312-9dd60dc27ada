-- <PERSON><PERSON><PERSON> OAuth2 Authorization Service Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS caidao_auth DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE caidao_auth;

-- User table
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar VARCHAR(200),
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE',
    last_login_time DATETIME,
    last_login_ip VARCHAR(50),
    login_count INT DEFAULT 0,
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIM<PERSON>,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN NOT NULL DEFAULT FALSE,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Role table
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(200),
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN NOT NULL DEFAULT FALSE,
    INDEX idx_name (name),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User role association table
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAuth2 client details table
CREATE TABLE IF NOT EXISTS oauth_client_details (
    client_id VARCHAR(256) PRIMARY KEY,
    resource_ids VARCHAR(256),
    client_secret VARCHAR(256),
    scope VARCHAR(256),
    authorized_grant_types VARCHAR(256),
    web_server_redirect_uri VARCHAR(256),
    authorities VARCHAR(256),
    access_token_validity INT,
    refresh_token_validity INT,
    additional_information VARCHAR(4096),
    autoapprove VARCHAR(256),
    client_name VARCHAR(100),
    client_description VARCHAR(500),
    client_uri VARCHAR(256),
    logo_uri VARCHAR(256),
    contact_email VARCHAR(100),
    tos_uri VARCHAR(256),
    policy_uri VARCHAR(256),
    jwks_uri VARCHAR(256),
    sector_identifier_uri VARCHAR(256),
    subject_type VARCHAR(50),
    id_token_signed_response_alg VARCHAR(50),
    id_token_encrypted_response_alg VARCHAR(50),
    id_token_encrypted_response_enc VARCHAR(50),
    userinfo_signed_response_alg VARCHAR(50),
    userinfo_encrypted_response_alg VARCHAR(50),
    userinfo_encrypted_response_enc VARCHAR(50),
    request_object_signing_alg VARCHAR(50),
    request_object_encryption_alg VARCHAR(50),
    request_object_encryption_enc VARCHAR(50),
    token_endpoint_auth_method VARCHAR(50),
    token_endpoint_auth_signing_alg VARCHAR(50),
    default_max_age INT,
    require_auth_time BOOLEAN DEFAULT FALSE,
    default_acr_values VARCHAR(256),
    initiate_login_uri VARCHAR(256),
    request_uris VARCHAR(1000),
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN NOT NULL DEFAULT FALSE,
    INDEX idx_enabled (enabled),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAuth2 access token table (for reference, Redis is used for storage)
CREATE TABLE IF NOT EXISTS oauth_access_token (
    token_id VARCHAR(256) PRIMARY KEY,
    token LONGBLOB,
    authentication_id VARCHAR(256) UNIQUE,
    user_name VARCHAR(256),
    client_id VARCHAR(256),
    authentication LONGBLOB,
    refresh_token VARCHAR(256),
    INDEX idx_authentication_id (authentication_id),
    INDEX idx_user_name (user_name),
    INDEX idx_client_id (client_id),
    INDEX idx_refresh_token (refresh_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAuth2 refresh token table (for reference, Redis is used for storage)
CREATE TABLE IF NOT EXISTS oauth_refresh_token (
    token_id VARCHAR(256) PRIMARY KEY,
    token LONGBLOB,
    authentication LONGBLOB
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAuth2 authorization code table (for reference, Redis is used for storage)
CREATE TABLE IF NOT EXISTS oauth_code (
    code VARCHAR(256) PRIMARY KEY,
    authentication LONGBLOB
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAuth2 client token table (for reference, Redis is used for storage)
CREATE TABLE IF NOT EXISTS oauth_client_token (
    token_id VARCHAR(256) PRIMARY KEY,
    token LONGBLOB,
    authentication_id VARCHAR(256) UNIQUE,
    user_name VARCHAR(256),
    client_id VARCHAR(256),
    INDEX idx_authentication_id (authentication_id),
    INDEX idx_user_name (user_name),
    INDEX idx_client_id (client_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
