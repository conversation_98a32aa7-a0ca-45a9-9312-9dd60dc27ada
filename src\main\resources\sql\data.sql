-- <PERSON><PERSON>ao OAuth2 Authorization Service Initial Data

USE caidao_auth;

-- Insert default roles
INSERT INTO sys_role (name, description, enabled, created_time, updated_time) VALUES
('ADMIN', 'System Administrator', TRUE, NOW(), NOW()),
('USER', 'Regular User', TRUE, NOW(), NOW()),
('GUEST', 'Guest User', TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Insert default admin user (password: admin123)
INSERT INTO sys_user (username, password, email, real_name, status, email_verified, created_time, updated_time) VALUES
('admin', '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', '<EMAIL>', 'System Administrator', 'ACTIVE', TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE email = VALUES(email);

-- Insert default test user (password: user123)
INSERT INTO sys_user (username, password, email, real_name, status, email_verified, created_time, updated_time) VALUES
('testuser', '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', '<EMAIL>', 'Test User', 'ACTIVE', TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE email = VALUES(email);

-- Assign roles to users
INSERT INTO sys_user_role (user_id, role_id, created_time) 
SELECT u.id, r.id, NOW()
FROM sys_user u, sys_role r 
WHERE u.username = 'admin' AND r.name = 'ADMIN'
ON DUPLICATE KEY UPDATE created_time = VALUES(created_time);

INSERT INTO sys_user_role (user_id, role_id, created_time) 
SELECT u.id, r.id, NOW()
FROM sys_user u, sys_role r 
WHERE u.username = 'testuser' AND r.name = 'USER'
ON DUPLICATE KEY UPDATE created_time = VALUES(created_time);

-- Insert default OAuth2 clients
INSERT INTO oauth_client_details (
    client_id, 
    client_secret, 
    scope, 
    authorized_grant_types, 
    web_server_redirect_uri, 
    authorities, 
    access_token_validity, 
    refresh_token_validity, 
    additional_information, 
    autoapprove,
    client_name,
    client_description,
    enabled,
    created_time,
    updated_time
) VALUES
(
    'caidao-web-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-web-secret
    'read,write',
    'authorization_code,refresh_token,password,client_credentials',
    'http://localhost:3000/callback,http://localhost:8080/callback',
    'ROLE_CLIENT',
    7200,
    2592000,
    '{"description":"Caidao Web Application Client"}',
    'read',
    'Caidao Web Client',
    'Official web application client for Caidao platform',
    TRUE,
    NOW(),
    NOW()
),
(
    'caidao-mobile-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-mobile-secret
    'read,write',
    'authorization_code,refresh_token,password',
    'caidao://oauth/callback',
    'ROLE_CLIENT',
    7200,
    2592000,
    '{"description":"Caidao Mobile Application Client"}',
    'read',
    'Caidao Mobile Client',
    'Official mobile application client for Caidao platform',
    TRUE,
    NOW(),
    NOW()
),
(
    'caidao-api-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-api-secret
    'read,write,admin',
    'client_credentials',
    NULL,
    'ROLE_CLIENT,ROLE_API',
    3600,
    NULL,
    '{"description":"Caidao API Client for server-to-server communication"}',
    'true',
    'Caidao API Client',
    'Server-to-server API client for Caidao platform',
    TRUE,
    NOW(),
    NOW()
),
(
    'caidao-test-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-test-secret
    'read',
    'authorization_code,refresh_token',
    'http://localhost:8080/test/callback',
    'ROLE_CLIENT',
    1800,
    86400,
    '{"description":"Test client for development and testing"}',
    'false',
    'Caidao Test Client',
    'Test client for development and testing purposes',
    TRUE,
    NOW(),
    NOW()
)
ON DUPLICATE KEY UPDATE 
    client_secret = VALUES(client_secret),
    scope = VALUES(scope),
    authorized_grant_types = VALUES(authorized_grant_types),
    web_server_redirect_uri = VALUES(web_server_redirect_uri),
    authorities = VALUES(authorities),
    access_token_validity = VALUES(access_token_validity),
    refresh_token_validity = VALUES(refresh_token_validity),
    additional_information = VALUES(additional_information),
    autoapprove = VALUES(autoapprove),
    client_name = VALUES(client_name),
    client_description = VALUES(client_description),
    updated_time = NOW();
