package com.caidao.auth.service;

import com.alibaba.fastjson.JSON;
import com.caidao.auth.entity.OAuthClient;
import com.caidao.auth.repository.OAuthClientRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * Custom Client Details Service
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class CustomClientDetailsService implements ClientDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomClientDetailsService.class);

    @Autowired
    private OAuthClientRepository clientRepository;

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        logger.debug("Loading client by client ID: {}", clientId);

        OAuthClient client = clientRepository.findByClientId(clientId)
                .orElseThrow(() -> new ClientRegistrationException("Client not found: " + clientId));

        if (!client.getEnabled()) {
            throw new ClientRegistrationException("Client is disabled: " + clientId);
        }

        BaseClientDetails clientDetails = new BaseClientDetails();
        clientDetails.setClientId(client.getClientId());
        clientDetails.setClientSecret(client.getClientSecret());

        // Set resource ids
        if (StringUtils.hasText(client.getResourceIds())) {
            clientDetails.setResourceIds(Arrays.asList(client.getResourceIds().split(",")));
        }

        // Set scopes
        if (StringUtils.hasText(client.getScope())) {
            clientDetails.setScope(Arrays.asList(client.getScope().split(",")));
        }

        // Set authorized grant types
        if (StringUtils.hasText(client.getAuthorizedGrantTypes())) {
            clientDetails.setAuthorizedGrantTypes(Arrays.asList(client.getAuthorizedGrantTypes().split(",")));
        }

        // Set redirect URIs
        if (StringUtils.hasText(client.getWebServerRedirectUri())) {
            clientDetails.setRegisteredRedirectUri(new HashSet<>(Arrays.asList(client.getWebServerRedirectUri().split(","))));
        }

        // Set authorities
        if (StringUtils.hasText(client.getAuthorities())) {
            clientDetails.setAuthorities(Arrays.asList(client.getAuthorities().split(","))
                    .stream()
                    .map(authority -> (org.springframework.security.core.GrantedAuthority) () -> authority)
                    .collect(java.util.stream.Collectors.toList()));
        }

        // Set token validity
        if (client.getAccessTokenValidity() != null) {
            clientDetails.setAccessTokenValiditySeconds(client.getAccessTokenValidity());
        }

        if (client.getRefreshTokenValidity() != null) {
            clientDetails.setRefreshTokenValiditySeconds(client.getRefreshTokenValidity());
        }

        // Set additional information
        if (StringUtils.hasText(client.getAdditionalInformation())) {
            try {
                Map<String, Object> additionalInfo = JSON.parseObject(client.getAdditionalInformation(), Map.class);
                clientDetails.setAdditionalInformation(additionalInfo);
            } catch (Exception e) {
                logger.warn("Failed to parse additional information for client: {}", clientId, e);
            }
        }

        // Set auto approve
        if (StringUtils.hasText(client.getAutoapprove())) {
            if ("true".equals(client.getAutoapprove())) {
                clientDetails.setAutoApproveScopes(clientDetails.getScope());
            } else if (!"false".equals(client.getAutoapprove())) {
                clientDetails.setAutoApproveScopes(Arrays.asList(client.getAutoapprove().split(",")));
            }
        }

        logger.debug("Client loaded successfully: {}", clientId);
        return clientDetails;
    }

    /**
     * Check if client exists
     */
    public boolean existsByClientId(String clientId) {
        return clientRepository.existsByClientId(clientId);
    }

    /**
     * Get all enabled clients
     */
    public List<OAuthClient> getAllEnabledClients() {
        return clientRepository.findAllEnabled();
    }
}
