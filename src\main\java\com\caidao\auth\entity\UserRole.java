package com.caidao.auth.entity;

import com.baomidou.mybatisplus.annotation.*;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * User Role Association Entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "sys_user_role")
@TableName("sys_user_role")
public class UserRole {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "role_id", nullable = false)
    private Long roleId;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    // Constructors
    public UserRole() {}

    public UserRole(Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
}
